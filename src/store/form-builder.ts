import { create } from 'zustand'
import { FormField, FieldType } from '@/types/form'
import { generateId } from '@/lib/utils'

interface FormBuilderState {
  fields: FormField[]
  selectedFieldId: string | null
  draggedFieldType: FieldType | null
  
  // Actions
  addField: (type: FieldType, index?: number) => void
  removeField: (id: string) => void
  updateField: (id: string, updates: Partial<FormField>) => void
  moveField: (fromIndex: number, toIndex: number) => void
  selectField: (id: string | null) => void
  setDraggedFieldType: (type: FieldType | null) => void
  clearFields: () => void
  setFields: (fields: FormField[]) => void
}

const createDefaultField = (type: FieldType): FormField => {
  const baseField = {
    id: generateId(),
    type,
    label: getDefaultLabel(type),
    required: false,
  }

  switch (type) {
    case 'text':
    case 'email':
    case 'password':
      return {
        ...baseField,
        placeholder: `Enter ${baseField.label.toLowerCase()}`,
      } as FormField

    case 'textarea':
      return {
        ...baseField,
        placeholder: `Enter ${baseField.label.toLowerCase()}`,
        rows: 4,
      } as FormField

    case 'number':
      return {
        ...baseField,
        placeholder: 'Enter number',
        min: 0,
        max: 100,
        step: 1,
      } as FormField

    case 'radio':
    case 'checkbox':
    case 'select':
    case 'multiselect':
      return {
        ...baseField,
        options: [
          { id: generateId(), label: 'Option 1', value: 'option1' },
          { id: generateId(), label: 'Option 2', value: 'option2' },
        ],
        multiple: type === 'multiselect' || type === 'checkbox',
      } as FormField

    case 'date':
      return {
        ...baseField,
      } as FormField

    case 'time':
      return {
        ...baseField,
      } as FormField

    case 'file':
    case 'image':
      return {
        ...baseField,
        accept: type === 'image' ? 'image/*' : '*/*',
        multiple: false,
        maxSize: 5 * 1024 * 1024, // 5MB
      } as FormField

    case 'rating':
      return {
        ...baseField,
        max: 5,
        icon: 'star',
      } as FormField

    case 'slider':
      return {
        ...baseField,
        min: 0,
        max: 100,
        step: 1,
        defaultValue: 50,
      } as FormField

    case 'switch':
      return {
        ...baseField,
        defaultValue: false,
      } as FormField

    case 'divider':
      return {
        ...baseField,
        label: '',
        style: 'solid',
      } as FormField

    case 'heading':
      return {
        ...baseField,
        level: 2,
      } as FormField

    case 'description':
      return {
        ...baseField,
        content: 'Add your description here',
      } as FormField

    default:
      return baseField as FormField
  }
}

const getDefaultLabel = (type: FieldType): string => {
  const labels: Record<FieldType, string> = {
    text: 'Text Input',
    textarea: 'Textarea',
    number: 'Number Input',
    email: 'Email Input',
    password: 'Password Input',
    radio: 'Radio Buttons',
    checkbox: 'Checkboxes',
    select: 'Select Dropdown',
    multiselect: 'Multi-Select',
    date: 'Date Picker',
    time: 'Time Picker',
    file: 'File Upload',
    image: 'Image Upload',
    rating: 'Rating',
    slider: 'Slider',
    switch: 'Switch',
    divider: 'Divider',
    heading: 'Heading',
    description: 'Description',
  }
  return labels[type]
}

export const useFormBuilder = create<FormBuilderState>((set, get) => ({
  fields: [],
  selectedFieldId: null,
  draggedFieldType: null,

  addField: (type: FieldType, index?: number) => {
    const newField = createDefaultField(type)
    set((state) => {
      const newFields = [...state.fields]
      if (index !== undefined) {
        newFields.splice(index, 0, newField)
      } else {
        newFields.push(newField)
      }
      return { fields: newFields, selectedFieldId: newField.id }
    })
  },

  removeField: (id: string) => {
    set((state) => ({
      fields: state.fields.filter((field) => field.id !== id),
      selectedFieldId: state.selectedFieldId === id ? null : state.selectedFieldId,
    }))
  },

  updateField: (id: string, updates: Partial<FormField>) => {
    set((state) => ({
      fields: state.fields.map((field) =>
        field.id === id ? { ...field, ...updates } : field
      ),
    }))
  },

  moveField: (fromIndex: number, toIndex: number) => {
    set((state) => {
      const newFields = [...state.fields]
      const [movedField] = newFields.splice(fromIndex, 1)
      newFields.splice(toIndex, 0, movedField)
      return { fields: newFields }
    })
  },

  selectField: (id: string | null) => {
    set({ selectedFieldId: id })
  },

  setDraggedFieldType: (type: FieldType | null) => {
    set({ draggedFieldType: type })
  },

  clearFields: () => {
    set({ fields: [], selectedFieldId: null })
  },

  setFields: (fields: FormField[]) => {
    set({ fields, selectedFieldId: null })
  },
}))
