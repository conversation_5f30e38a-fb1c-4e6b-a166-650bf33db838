import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { data } = body

    if (!data) {
      return NextResponse.json({ error: 'Response data is required' }, { status: 400 })
    }

    // Check if form exists and is published
    const form = await prisma.form.findUnique({
      where: {
        id: params.id
      }
    })

    if (!form) {
      return NextResponse.json({ error: 'Form not found' }, { status: 404 })
    }

    if (!form.isPublished) {
      return NextResponse.json({ error: 'Form is not published' }, { status: 403 })
    }

    // Create form response
    const response = await prisma.formResponse.create({
      data: {
        formId: params.id,
        data
      }
    })

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error creating form response:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const responses = await prisma.formResponse.findMany({
      where: {
        formId: params.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(responses)
  } catch (error) {
    console.error('Error fetching form responses:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
