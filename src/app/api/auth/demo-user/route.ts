import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, name } = body

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json({ message: 'User already exists' })
    }

    // Create new user
    const user = await prisma.user.create({
      data: {
        email,
        name,
        role: 'user'
      }
    })

    return NextResponse.json({ message: 'Demo user created successfully', user })
  } catch (error) {
    console.error('Error creating demo user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
