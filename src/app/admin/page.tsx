'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Plus, Edit, Trash2, Eye, Copy, LogOut } from 'lucide-react'

interface Form {
  id: string
  title: string
  description?: string
  isPublished: boolean
  createdAt: string
  updatedAt: string
  _count: {
    responses: number
  }
}

export default function AdminDashboard() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [forms, setForms] = useState<Form[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    } else if (status === 'authenticated') {
      loadForms()
    }
  }, [status, router])

  const loadForms = async () => {
    try {
      const response = await fetch('/api/forms')
      if (response.ok) {
        const data = await response.json()
        setForms(data)
      } else {
        console.error('Failed to load forms')
      }
    } catch (error) {
      console.error('Error loading forms:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateForm = () => {
    router.push('/admin/builder')
  }

  const handleEditForm = (formId: string) => {
    router.push(`/admin/builder?id=${formId}`)
  }

  const handleDeleteForm = async (formId: string) => {
    if (!confirm('Are you sure you want to delete this form?')) return

    try {
      const response = await fetch(`/api/forms/${formId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setForms(forms.filter(form => form.id !== formId))
      } else {
        alert('Failed to delete form')
      }
    } catch (error) {
      console.error('Error deleting form:', error)
      alert('Error deleting form')
    }
  }

  const handleTogglePublish = async (formId: string, isPublished: boolean) => {
    try {
      const form = forms.find(f => f.id === formId)
      if (!form) return

      const response = await fetch(`/api/forms/${formId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...form,
          isPublished: !isPublished,
        }),
      })

      if (response.ok) {
        setForms(forms.map(f => 
          f.id === formId ? { ...f, isPublished: !isPublished } : f
        ))
      } else {
        alert('Failed to update form')
      }
    } catch (error) {
      console.error('Error updating form:', error)
      alert('Error updating form')
    }
  }

  const handleCopyLink = (formId: string) => {
    const url = `${window.location.origin}/form/${formId}`
    navigator.clipboard.writeText(url)
    alert('Form link copied to clipboard!')
  }

  const handleViewResponses = (formId: string) => {
    router.push(`/admin/responses?formId=${formId}`)
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Form Builder Dashboard</h1>
            <p className="text-gray-600">Welcome back, {session?.user?.name || session?.user?.email}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={handleCreateForm} className="flex items-center space-x-2">
              <Plus size={16} />
              <span>Create New Form</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => signOut()}
              className="flex items-center space-x-2"
            >
              <LogOut size={16} />
              <span>Sign Out</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {forms.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg
                className="mx-auto h-24 w-24"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No forms yet</h3>
            <p className="text-gray-500 mb-6">Get started by creating your first form</p>
            <Button onClick={handleCreateForm} className="flex items-center space-x-2">
              <Plus size={16} />
              <span>Create Your First Form</span>
            </Button>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {forms.map((form) => (
              <div key={form.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{form.title}</h3>
                    {form.description && (
                      <p className="text-gray-600 text-sm">{form.description}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      form.isPublished 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {form.isPublished ? 'Published' : 'Draft'}
                    </span>
                  </div>
                </div>

                <div className="text-sm text-gray-500 mb-4">
                  <p>{form._count.responses} responses</p>
                  <p>Updated {new Date(form.updatedAt).toLocaleDateString()}</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditForm(form.id)}
                      className="flex items-center space-x-1"
                    >
                      <Edit size={12} />
                      <span>Edit</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewResponses(form.id)}
                      className="flex items-center space-x-1"
                    >
                      <Eye size={12} />
                      <span>Responses</span>
                    </Button>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopyLink(form.id)}
                      className="h-6 w-6 p-0"
                    >
                      <Copy size={12} />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleTogglePublish(form.id, form.isPublished)}
                      className="h-6 w-6 p-0"
                    >
                      {form.isPublished ? '📴' : '📢'}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeleteForm(form.id)}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={12} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
