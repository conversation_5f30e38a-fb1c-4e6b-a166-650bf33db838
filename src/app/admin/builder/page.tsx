'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useFormBuilder } from '@/store/form-builder'
import { FieldToolbar } from '@/components/form-builder/field-toolbar'
import { FormCanvas } from '@/components/form-builder/form-canvas'
import { FieldProperties } from '@/components/form-builder/field-properties'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Save, Eye, ArrowLeft } from 'lucide-react'
import { FieldType } from '@/types/form'

export default function FormBuilderPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()
  const formId = searchParams.get('id')

  const {
    fields,
    selectedFieldId,
    addField,
    selectField,
    clearFields,
    setFields,
  } = useFormBuilder()

  const [formTitle, setFormTitle] = useState('Untitled Form')
  const [formDescription, setFormDescription] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Load existing form if editing
  useEffect(() => {
    if (formId && session) {
      loadForm(formId)
    }
  }, [formId, session])

  const loadForm = async (id: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/forms/${id}`)
      if (response.ok) {
        const form = await response.json()
        setFormTitle(form.title)
        setFormDescription(form.description || '')
        setFields(form.fields)
      } else {
        console.error('Failed to load form')
      }
    } catch (error) {
      console.error('Error loading form:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!session) return

    setIsSaving(true)
    try {
      const formData = {
        title: formTitle,
        description: formDescription,
        fields: fields,
      }

      const url = formId ? `/api/forms/${formId}` : '/api/forms'
      const method = formId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const savedForm = await response.json()
        if (!formId) {
          router.push(`/admin/builder?id=${savedForm.id}`)
        }
        alert('Form saved successfully!')
      } else {
        alert('Failed to save form')
      }
    } catch (error) {
      console.error('Error saving form:', error)
      alert('Error saving form')
    } finally {
      setIsSaving(false)
    }
  }

  const handlePreview = () => {
    if (formId) {
      window.open(`/form/${formId}`, '_blank')
    } else {
      alert('Please save the form first to preview it')
    }
  }

  const handleFieldSelect = (type: FieldType) => {
    addField(type)
  }

  const handleFieldEdit = (fieldId: string) => {
    selectField(fieldId)
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/admin')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft size={16} />
              <span>Back to Dashboard</span>
            </Button>
            <div className="border-l border-gray-300 pl-4">
              <div className="space-y-1">
                <Label htmlFor="form-title" className="text-sm">Form Title</Label>
                <Input
                  id="form-title"
                  value={formTitle}
                  onChange={(e) => setFormTitle(e.target.value)}
                  className="text-lg font-semibold border-none p-0 h-auto focus-visible:ring-0"
                  placeholder="Enter form title"
                />
              </div>
              <div className="mt-2">
                <Label htmlFor="form-description" className="text-sm">Description</Label>
                <Input
                  id="form-description"
                  value={formDescription}
                  onChange={(e) => setFormDescription(e.target.value)}
                  className="text-sm text-gray-600 border-none p-0 h-auto focus-visible:ring-0"
                  placeholder="Enter form description (optional)"
                />
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handlePreview}
              disabled={!formId}
              className="flex items-center space-x-2"
            >
              <Eye size={16} />
              <span>Preview</span>
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center space-x-2"
            >
              <Save size={16} />
              <span>{isSaving ? 'Saving...' : 'Save'}</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        <FieldToolbar onFieldSelect={handleFieldSelect} />
        <FormCanvas onFieldEdit={handleFieldEdit} />
        <FieldProperties fieldId={selectedFieldId} />
      </div>
    </div>
  )
}
