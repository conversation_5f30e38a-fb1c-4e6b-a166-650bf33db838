import React, { useState } from 'react'
import { useForm<PERSON>uilder } from '@/store/form-builder'
import { DraggableField } from './draggable-field'
import { FieldType } from '@/types/form'

interface FormCanvasProps {
  onFieldEdit: (fieldId: string) => void
}

export function FormCanvas({ onFieldEdit }: FormCanvasProps) {
  const {
    fields,
    selectedFieldId,
    addField,
    removeField,
    moveField,
    selectField,
  } = useFormBuilder()

  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(index)
  }

  const handleDrop = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(null)

    const fieldType = e.dataTransfer.getData('fieldType') as FieldType
    const draggedFieldIndex = e.dataTransfer.getData('fieldIndex')

    if (fieldType) {
      // Adding new field from toolbar
      addField(fieldType, index)
    } else if (draggedFieldIndex) {
      // Moving existing field
      const fromIndex = parseInt(draggedFieldIndex)
      if (fromIndex !== index) {
        moveField(fromIndex, index)
      }
    }
  }

  const handleFieldDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('fieldIndex', index.toString())
  }

  const handleCanvasDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOverIndex(null)

    const fieldType = e.dataTransfer.getData('fieldType') as FieldType
    if (fieldType) {
      addField(fieldType)
    }
  }

  const handleCanvasDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  if (fields.length === 0) {
    return (
      <div
        className="flex-1 flex items-center justify-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg m-4"
        onDrop={handleCanvasDrop}
        onDragOver={handleCanvasDragOver}
      >
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <svg
              className="mx-auto h-12 w-12"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            Start building your form
          </h3>
          <p className="text-gray-500">
            Drag and drop fields from the sidebar to get started
          </p>
        </div>
      </div>
    )
  }

  return (
    <div
      className="flex-1 p-4 overflow-y-auto"
      onDrop={handleCanvasDrop}
      onDragOver={handleCanvasDragOver}
    >
      <div className="max-w-2xl mx-auto space-y-4">
        {fields.map((field, index) => (
          <React.Fragment key={field.id}>
            {/* Drop zone before field */}
            <div
              className={`h-2 transition-all ${
                dragOverIndex === index
                  ? 'bg-blue-200 border-2 border-blue-400 border-dashed rounded'
                  : 'hover:bg-gray-100 rounded'
              }`}
              onDragOver={(e) => handleDragOver(e, index)}
              onDrop={(e) => handleDrop(e, index)}
            />

            <DraggableField
              field={field}
              index={index}
              isSelected={selectedFieldId === field.id}
              onSelect={() => selectField(field.id)}
              onDelete={() => removeField(field.id)}
              onEdit={() => onFieldEdit(field.id)}
              onDragStart={(e) => handleFieldDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index + 1)}
              onDrop={(e) => handleDrop(e, index + 1)}
            />
          </React.Fragment>
        ))}

        {/* Drop zone after last field */}
        <div
          className={`h-2 transition-all ${
            dragOverIndex === fields.length
              ? 'bg-blue-200 border-2 border-blue-400 border-dashed rounded'
              : 'hover:bg-gray-100 rounded'
          }`}
          onDragOver={(e) => handleDragOver(e, fields.length)}
          onDrop={(e) => handleDrop(e, fields.length)}
        />
      </div>
    </div>
  )
}
