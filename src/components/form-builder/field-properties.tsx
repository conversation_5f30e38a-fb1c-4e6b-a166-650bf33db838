import React from 'react'
import { useForm<PERSON>uilder } from '@/store/form-builder'
import { FormField, SelectField, FieldOption } from '@/types/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Plus, Trash2 } from 'lucide-react'
import { generateId } from '@/lib/utils'

interface FieldPropertiesProps {
  fieldId: string | null
}

export function FieldProperties({ fieldId }: FieldPropertiesProps) {
  const { fields, updateField } = useFormBuilder()

  const field = fields.find(f => f.id === fieldId)

  if (!field) {
    return (
      <div className="w-80 bg-white border-l border-gray-200 p-4">
        <div className="text-center text-gray-500 mt-8">
          <p>Select a field to edit its properties</p>
        </div>
      </div>
    )
  }

  const handleFieldUpdate = (updates: Partial<FormField>) => {
    updateField(field.id, updates)
  }

  const handleOptionAdd = () => {
    if ('options' in field) {
      const newOption: FieldOption = {
        id: generateId(),
        label: `Option ${field.options.length + 1}`,
        value: `option${field.options.length + 1}`
      }
      handleFieldUpdate({
        options: [...field.options, newOption]
      })
    }
  }

  const handleOptionUpdate = (optionId: string, updates: Partial<FieldOption>) => {
    if ('options' in field) {
      const updatedOptions = field.options.map(option =>
        option.id === optionId ? { ...option, ...updates } : option
      )
      handleFieldUpdate({ options: updatedOptions })
    }
  }

  const handleOptionDelete = (optionId: string) => {
    if ('options' in field) {
      const updatedOptions = field.options.filter(option => option.id !== optionId)
      handleFieldUpdate({ options: updatedOptions })
    }
  }

  return (
    <div className="w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">Field Properties</h3>

      <div className="space-y-4">
        {/* Basic Properties */}
        <div>
          <Label htmlFor="field-label">Label</Label>
          <Input
            id="field-label"
            value={field.label}
            onChange={(e) => handleFieldUpdate({ label: e.target.value })}
            placeholder="Field label"
          />
        </div>

        {field.type !== 'divider' && field.type !== 'heading' && field.type !== 'description' && (
          <div>
            <Label htmlFor="field-placeholder">Placeholder</Label>
            <Input
              id="field-placeholder"
              value={field.placeholder || ''}
              onChange={(e) => handleFieldUpdate({ placeholder: e.target.value })}
              placeholder="Placeholder text"
            />
          </div>
        )}

        <div>
          <Label htmlFor="field-description">Description</Label>
          <Input
            id="field-description"
            value={field.description || ''}
            onChange={(e) => handleFieldUpdate({ description: e.target.value })}
            placeholder="Help text"
          />
        </div>

        {field.type !== 'divider' && field.type !== 'heading' && field.type !== 'description' && (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="field-required"
              checked={field.required || false}
              onChange={(e) => handleFieldUpdate({ required: e.target.checked })}
              className="h-4 w-4"
            />
            <Label htmlFor="field-required">Required field</Label>
          </div>
        )}

        {/* Type-specific Properties */}
        {(field.type === 'text' || field.type === 'textarea' || field.type === 'email' || field.type === 'password') && (
          <>
            <div>
              <Label htmlFor="field-min-length">Minimum Length</Label>
              <Input
                id="field-min-length"
                type="number"
                value={(field as any).minLength || ''}
                onChange={(e) => handleFieldUpdate({ minLength: parseInt(e.target.value) || undefined })}
                placeholder="0"
              />
            </div>
            <div>
              <Label htmlFor="field-max-length">Maximum Length</Label>
              <Input
                id="field-max-length"
                type="number"
                value={(field as any).maxLength || ''}
                onChange={(e) => handleFieldUpdate({ maxLength: parseInt(e.target.value) || undefined })}
                placeholder="100"
              />
            </div>
          </>
        )}

        {field.type === 'textarea' && (
          <div>
            <Label htmlFor="field-rows">Rows</Label>
            <Input
              id="field-rows"
              type="number"
              value={(field as any).rows || 4}
              onChange={(e) => handleFieldUpdate({ rows: parseInt(e.target.value) || 4 })}
              min="1"
              max="20"
            />
          </div>
        )}

        {field.type === 'number' && (
          <>
            <div>
              <Label htmlFor="field-min">Minimum Value</Label>
              <Input
                id="field-min"
                type="number"
                value={(field as any).min || ''}
                onChange={(e) => handleFieldUpdate({ min: parseInt(e.target.value) || undefined })}
                placeholder="0"
              />
            </div>
            <div>
              <Label htmlFor="field-max">Maximum Value</Label>
              <Input
                id="field-max"
                type="number"
                value={(field as any).max || ''}
                onChange={(e) => handleFieldUpdate({ max: parseInt(e.target.value) || undefined })}
                placeholder="100"
              />
            </div>
            <div>
              <Label htmlFor="field-step">Step</Label>
              <Input
                id="field-step"
                type="number"
                value={(field as any).step || 1}
                onChange={(e) => handleFieldUpdate({ step: parseInt(e.target.value) || 1 })}
                min="1"
              />
            </div>
          </>
        )}

        {/* Options for select fields */}
        {(field.type === 'radio' || field.type === 'checkbox' || field.type === 'select' || field.type === 'multiselect') && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label>Options</Label>
              <Button size="sm" onClick={handleOptionAdd} className="h-6 w-6 p-0">
                <Plus size={12} />
              </Button>
            </div>
            <div className="space-y-2">
              {(field as SelectField).options.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Input
                    value={option.label}
                    onChange={(e) => handleOptionUpdate(option.id, { label: e.target.value })}
                    placeholder="Option label"
                    className="flex-1"
                  />
                  <Input
                    value={option.value}
                    onChange={(e) => handleOptionUpdate(option.id, { value: e.target.value })}
                    placeholder="Value"
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleOptionDelete(option.id)}
                    className="h-6 w-6 p-0 text-red-500"
                  >
                    <Trash2 size={12} />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {field.type === 'heading' && (
          <div>
            <Label htmlFor="field-level">Heading Level</Label>
            <select
              id="field-level"
              value={(field as any).level || 2}
              onChange={(e) => handleFieldUpdate({ level: parseInt(e.target.value) as any })}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
            >
              {[1, 2, 3, 4, 5, 6].map(level => (
                <option key={level} value={level}>H{level}</option>
              ))}
            </select>
          </div>
        )}

        {field.type === 'description' && (
          <div>
            <Label htmlFor="field-content">Content</Label>
            <textarea
              id="field-content"
              value={(field as any).content || ''}
              onChange={(e) => handleFieldUpdate({ content: e.target.value })}
              placeholder="Description content"
              rows={4}
              className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
            />
          </div>
        )}
      </div>
    </div>
  )
}
