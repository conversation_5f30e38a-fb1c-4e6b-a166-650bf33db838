import React from 'react'
import { FieldType } from '@/types/form'
import { 
  Type, 
  AlignLeft, 
  Hash, 
  Mail, 
  Lock, 
  Circle, 
  Square, 
  ChevronDown, 
  Calendar, 
  Clock, 
  Upload, 
  Image, 
  Star, 
  Sliders, 
  ToggleLeft, 
  Minus, 
  Heading, 
  FileText 
} from 'lucide-react'

interface FieldToolbarProps {
  onFieldSelect: (type: FieldType) => void
}

const fieldTypes: Array<{
  type: FieldType
  label: string
  icon: React.ReactNode
  category: string
}> = [
  // Input Fields
  { type: 'text', label: 'Text Input', icon: <Type size={16} />, category: 'Input' },
  { type: 'textarea', label: 'Textarea', icon: <AlignLeft size={16} />, category: 'Input' },
  { type: 'number', label: 'Number', icon: <Hash size={16} />, category: 'Input' },
  { type: 'email', label: 'Email', icon: <Mail size={16} />, category: 'Input' },
  { type: 'password', label: 'Password', icon: <Lock size={16} />, category: 'Input' },
  
  // Selection Fields
  { type: 'radio', label: 'Radio Buttons', icon: <Circle size={16} />, category: 'Selection' },
  { type: 'checkbox', label: 'Checkboxes', icon: <Square size={16} />, category: 'Selection' },
  { type: 'select', label: 'Dropdown', icon: <ChevronDown size={16} />, category: 'Selection' },
  { type: 'multiselect', label: 'Multi-Select', icon: <ChevronDown size={16} />, category: 'Selection' },
  
  // Date & Time
  { type: 'date', label: 'Date Picker', icon: <Calendar size={16} />, category: 'Date & Time' },
  { type: 'time', label: 'Time Picker', icon: <Clock size={16} />, category: 'Date & Time' },
  
  // File Upload
  { type: 'file', label: 'File Upload', icon: <Upload size={16} />, category: 'File' },
  { type: 'image', label: 'Image Upload', icon: <Image size={16} />, category: 'File' },
  
  // Interactive
  { type: 'rating', label: 'Rating', icon: <Star size={16} />, category: 'Interactive' },
  { type: 'slider', label: 'Slider', icon: <Sliders size={16} />, category: 'Interactive' },
  { type: 'switch', label: 'Switch', icon: <ToggleLeft size={16} />, category: 'Interactive' },
  
  // Layout
  { type: 'divider', label: 'Divider', icon: <Minus size={16} />, category: 'Layout' },
  { type: 'heading', label: 'Heading', icon: <Heading size={16} />, category: 'Layout' },
  { type: 'description', label: 'Description', icon: <FileText size={16} />, category: 'Layout' },
]

const categories = ['Input', 'Selection', 'Date & Time', 'File', 'Interactive', 'Layout']

export function FieldToolbar({ onFieldSelect }: FieldToolbarProps) {
  return (
    <div className="w-64 bg-white border-r border-gray-200 p-4 overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">Form Fields</h3>
      
      {categories.map((category) => (
        <div key={category} className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">{category}</h4>
          <div className="space-y-1">
            {fieldTypes
              .filter((field) => field.category === category)
              .map((field) => (
                <button
                  key={field.type}
                  onClick={() => onFieldSelect(field.type)}
                  className="w-full flex items-center space-x-2 p-2 text-left text-sm hover:bg-gray-100 rounded-md transition-colors"
                  draggable
                  onDragStart={(e) => {
                    e.dataTransfer.setData('fieldType', field.type)
                  }}
                >
                  <span className="text-gray-500">{field.icon}</span>
                  <span>{field.label}</span>
                </button>
              ))}
          </div>
        </div>
      ))}
    </div>
  )
}
