import React from 'react'
import { <PERSON>Field } from '@/types/form'
import { <PERSON><PERSON>enderer } from '@/components/form-fields/field-renderer'
import { Button } from '@/components/ui/button'
import { Trash2, Settings, GripVertical } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DraggableFieldProps {
  field: FormField
  index: number
  isSelected: boolean
  onSelect: () => void
  onDelete: () => void
  onEdit: () => void
  onDragStart: (e: React.DragEvent) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent) => void
}

export function DraggableField({
  field,
  index,
  isSelected,
  onSelect,
  onDelete,
  onEdit,
  onDragStart,
  onDragOver,
  onDrop,
}: DraggableFieldProps) {
  return (
    <div
      className={cn(
        "relative group border-2 border-dashed border-transparent rounded-lg p-4 transition-all",
        isSelected && "border-blue-500 bg-blue-50",
        "hover:border-gray-300"
      )}
      onClick={onSelect}
      onDragOver={onDragOver}
      onDrop={onDrop}
    >
      {/* Drag Handle */}
      <div
        className="absolute left-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
        draggable
        onDragStart={onDragStart}
      >
        <GripVertical size={16} className="text-gray-400" />
      </div>

      {/* Field Actions */}
      <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
        <Button
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation()
            onEdit()
          }}
          className="h-6 w-6 p-0"
        >
          <Settings size={12} />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation()
            onDelete()
          }}
          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
        >
          <Trash2 size={12} />
        </Button>
      </div>

      {/* Field Content */}
      <div className="ml-6 mr-16">
        <FieldRenderer field={field} preview={true} />
      </div>

      {/* Drop Zone Indicator */}
      <div className="absolute inset-0 border-2 border-blue-500 border-dashed bg-blue-50 opacity-0 pointer-events-none transition-opacity" />
    </div>
  )
}
