import React from 'react'
import { FormField } from '@/types/form'
import { TextField } from './text-field'
import { TextareaField } from './textarea-field'
import { SelectField } from './select-field'
import { Label } from '@/components/ui/label'

interface FieldRendererProps {
  field: FormField
  value?: any
  onChange?: (value: any) => void
  preview?: boolean
}

export function FieldRenderer({ field, value, onChange, preview = false }: FieldRendererProps) {
  switch (field.type) {
    case 'text':
    case 'email':
    case 'password':
      return (
        <TextField
          field={field as any}
          value={value}
          onChange={onChange}
          preview={preview}
        />
      )

    case 'textarea':
      return (
        <TextareaField
          field={field as any}
          value={value}
          onChange={onChange}
          preview={preview}
        />
      )

    case 'number':
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-sm font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.description && (
            <p className="text-sm text-gray-600">{field.description}</p>
          )}
          <input
            id={field.id}
            type="number"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange?.(e.target.value)}
            required={field.required}
            min={(field as any).min}
            max={(field as any).max}
            step={(field as any).step}
            disabled={preview}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
      )

    case 'radio':
    case 'checkbox':
    case 'select':
    case 'multiselect':
      return (
        <SelectField
          field={field as any}
          value={value}
          onChange={onChange}
          preview={preview}
        />
      )

    case 'date':
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-sm font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.description && (
            <p className="text-sm text-gray-600">{field.description}</p>
          )}
          <input
            id={field.id}
            type="date"
            value={value || ''}
            onChange={(e) => onChange?.(e.target.value)}
            required={field.required}
            min={(field as any).min}
            max={(field as any).max}
            disabled={preview}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
      )

    case 'time':
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-sm font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.description && (
            <p className="text-sm text-gray-600">{field.description}</p>
          )}
          <input
            id={field.id}
            type="time"
            value={value || ''}
            onChange={(e) => onChange?.(e.target.value)}
            required={field.required}
            disabled={preview}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
      )

    case 'file':
    case 'image':
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-sm font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.description && (
            <p className="text-sm text-gray-600">{field.description}</p>
          )}
          <input
            id={field.id}
            type="file"
            accept={(field as any).accept}
            multiple={(field as any).multiple}
            onChange={(e) => onChange?.(e.target.files)}
            required={field.required}
            disabled={preview}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
      )

    case 'switch':
      return (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              id={field.id}
              type="checkbox"
              checked={value || false}
              onChange={(e) => onChange?.(e.target.checked)}
              disabled={preview}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <Label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
          </div>
          {field.description && (
            <p className="text-sm text-gray-600">{field.description}</p>
          )}
        </div>
      )

    case 'divider':
      return (
        <div className="my-6">
          <hr className={`border-gray-300 ${(field as any).style === 'dashed' ? 'border-dashed' : (field as any).style === 'dotted' ? 'border-dotted' : 'border-solid'}`} />
        </div>
      )

    case 'heading':
      const HeadingTag = `h${(field as any).level || 2}` as keyof JSX.IntrinsicElements
      return (
        <HeadingTag className="font-bold text-gray-900 mb-2">
          {field.label}
        </HeadingTag>
      )

    case 'description':
      return (
        <div className="text-gray-600">
          {(field as any).content}
        </div>
      )

    default:
      return (
        <div className="p-4 border border-red-300 bg-red-50 rounded-md">
          <p className="text-red-600">Unsupported field type: {field.type}</p>
        </div>
      )
  }
}
