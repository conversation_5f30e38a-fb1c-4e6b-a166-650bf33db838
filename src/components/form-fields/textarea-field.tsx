import React from 'react'
import { TextareaField as TextareaFieldType } from '@/types/form'
import { Label } from '@/components/ui/label'

interface TextareaFieldProps {
  field: TextareaFieldType
  value?: string
  onChange?: (value: string) => void
  preview?: boolean
}

export function TextareaField({ field, value = '', onChange, preview = false }: TextareaFieldProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor={field.id} className="text-sm font-medium">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {field.description && (
        <p className="text-sm text-gray-600">{field.description}</p>
      )}
      <textarea
        id={field.id}
        placeholder={field.placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        required={field.required}
        minLength={field.minLength}
        maxLength={field.maxLength}
        rows={field.rows || 4}
        disabled={preview}
        className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-vertical"
      />
    </div>
  )
}
