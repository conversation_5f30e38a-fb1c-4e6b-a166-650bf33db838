import React from 'react'
import { TextField as TextFieldType } from '@/types/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface TextFieldProps {
  field: TextFieldType
  value?: string
  onChange?: (value: string) => void
  preview?: boolean
}

export function TextField({ field, value = '', onChange, preview = false }: TextFieldProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor={field.id} className="text-sm font-medium">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {field.description && (
        <p className="text-sm text-gray-600">{field.description}</p>
      )}
      <Input
        id={field.id}
        type={field.type}
        placeholder={field.placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        required={field.required}
        minLength={field.minLength}
        maxLength={field.maxLength}
        disabled={preview}
        className="w-full"
      />
    </div>
  )
}
