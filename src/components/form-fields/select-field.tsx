import React from 'react'
import { SelectField as SelectFieldType } from '@/types/form'
import { Label } from '@/components/ui/label'

interface SelectFieldProps {
  field: SelectFieldType
  value?: string | string[]
  onChange?: (value: string | string[]) => void
  preview?: boolean
}

export function SelectField({ field, value, onChange, preview = false }: SelectFieldProps) {
  const handleRadioChange = (optionValue: string) => {
    onChange?.(optionValue)
  }

  const handleCheckboxChange = (optionValue: string) => {
    const currentValues = Array.isArray(value) ? value : []
    const newValues = currentValues.includes(optionValue)
      ? currentValues.filter(v => v !== optionValue)
      : [...currentValues, optionValue]
    onChange?.(newValues)
  }

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (field.multiple) {
      const selectedValues = Array.from(e.target.selectedOptions, option => option.value)
      onChange?.(selectedValues)
    } else {
      onChange?.(e.target.value)
    }
  }

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {field.description && (
        <p className="text-sm text-gray-600">{field.description}</p>
      )}

      {field.type === 'radio' && (
        <div className="space-y-2">
          {field.options.map((option) => (
            <div key={option.id} className="flex items-center space-x-2">
              <input
                type="radio"
                id={`${field.id}-${option.id}`}
                name={field.id}
                value={option.value}
                checked={value === option.value}
                onChange={() => handleRadioChange(option.value)}
                disabled={preview}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <Label htmlFor={`${field.id}-${option.id}`} className="text-sm">
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      )}

      {field.type === 'checkbox' && (
        <div className="space-y-2">
          {field.options.map((option) => (
            <div key={option.id} className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={`${field.id}-${option.id}`}
                value={option.value}
                checked={Array.isArray(value) && value.includes(option.value)}
                onChange={() => handleCheckboxChange(option.value)}
                disabled={preview}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <Label htmlFor={`${field.id}-${option.id}`} className="text-sm">
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      )}

      {(field.type === 'select' || field.type === 'multiselect') && (
        <select
          id={field.id}
          value={Array.isArray(value) ? value : [value || '']}
          onChange={handleSelectChange}
          multiple={field.multiple}
          required={field.required}
          disabled={preview}
          className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        >
          {!field.multiple && (
            <option value="">Select an option</option>
          )}
          {field.options.map((option) => (
            <option key={option.id} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}
    </div>
  )
}
