export type FieldType = 
  | 'text'
  | 'textarea'
  | 'number'
  | 'email'
  | 'password'
  | 'radio'
  | 'checkbox'
  | 'select'
  | 'multiselect'
  | 'date'
  | 'time'
  | 'file'
  | 'image'
  | 'rating'
  | 'slider'
  | 'switch'
  | 'divider'
  | 'heading'
  | 'description'

export interface FieldOption {
  id: string
  label: string
  value: string
}

export interface BaseField {
  id: string
  type: FieldType
  label: string
  placeholder?: string
  required?: boolean
  description?: string
}

export interface TextField extends BaseField {
  type: 'text' | 'email' | 'password'
  minLength?: number
  maxLength?: number
}

export interface TextareaField extends BaseField {
  type: 'textarea'
  rows?: number
  minLength?: number
  maxLength?: number
}

export interface NumberField extends BaseField {
  type: 'number'
  min?: number
  max?: number
  step?: number
}

export interface SelectField extends BaseField {
  type: 'radio' | 'checkbox' | 'select' | 'multiselect'
  options: FieldOption[]
  multiple?: boolean
}

export interface DateField extends BaseField {
  type: 'date' | 'time'
  min?: string
  max?: string
}

export interface FileField extends BaseField {
  type: 'file' | 'image'
  accept?: string
  multiple?: boolean
  maxSize?: number
}

export interface RatingField extends BaseField {
  type: 'rating'
  max?: number
  icon?: string
}

export interface SliderField extends BaseField {
  type: 'slider'
  min?: number
  max?: number
  step?: number
  defaultValue?: number
}

export interface SwitchField extends BaseField {
  type: 'switch'
  defaultValue?: boolean
}

export interface DividerField extends BaseField {
  type: 'divider'
  style?: 'solid' | 'dashed' | 'dotted'
}

export interface HeadingField extends BaseField {
  type: 'heading'
  level?: 1 | 2 | 3 | 4 | 5 | 6
}

export interface DescriptionField extends BaseField {
  type: 'description'
  content: string
}

export type FormField = 
  | TextField
  | TextareaField
  | NumberField
  | SelectField
  | DateField
  | FileField
  | RatingField
  | SliderField
  | SwitchField
  | DividerField
  | HeadingField
  | DescriptionField

export interface Form {
  id: string
  title: string
  description?: string
  fields: FormField[]
  isPublished: boolean
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface FormResponse {
  id: string
  formId: string
  data: Record<string, any>
  createdAt: Date
}
